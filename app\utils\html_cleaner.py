import re
import codecs
from bs4 import BeautifulSoup, NavigableString

def clean_html_content(content):
    """
    Clean HTML content to ensure only allowed tags are present and remove excessive <br> tags.
    Allowed tags:
    - <p> for paragraphs
    - <br> for line breaks within paragraphs
    - <blockquote> for decorative quotes
    - <ul> for bullet lists
    - <ol> for numbered lists
    - <li> for list items
    - <b>, <strong> for bold
    - <i>, <em> for italic
    - <u> for underline
    - <s>, <del> for strikethrough
    - <code> for inline code (monospace)
    """
    # Decode bytes as UTF-8 if needed
    if isinstance(content, bytes):
        content = content.decode('utf-8', errors='replace')

    # Only decode as unicode_escape if it looks like escaped unicode (e.g., \\uXXXX)
    if re.search(r'\\u[0-9a-fA-F]{4}', content):
        try:
            content = codecs.decode(content, 'unicode_escape')
        except Exception:
            pass  # If decoding fails, leave content as is

    # Replace all newline variants with <br>
    content = re.sub(r'(\n\n|\n|\r\n|\r)', '<br>', content)

    # Convert **bold** to <strong> and *italic* to <em>
    # Handle bold first (double asterisks) to avoid conflicts
    content = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', content)

    # Handle italic (single asterisks) - more comprehensive pattern
    # This pattern handles asterisks around words/phrases, including those with spaces
    content = re.sub(r'\*([^*\n]+?)\*', r'<em>\1</em>', content)

    # Clean up any remaining single asterisks that weren't part of markdown formatting
    # Remove standalone asterisks or asterisks that couldn't be properly paired
    content = re.sub(r'(?<!\w)\*(?!\w)', '', content)  # Remove asterisks not adjacent to word characters
    content = re.sub(r'\*+', '', content)  # Remove any remaining multiple asterisks

    # Parse the HTML content
    soup = BeautifulSoup(content, 'html.parser')
    
    # Define allowed tags and their attributes
    allowed_tags = {
        'p': ['class'],  # Allow class attribute for hashtags
        'br': [],
        'blockquote': [],
        'ul': [],
        'ol': [],
        'li': [],
        'b': [],
        'strong': [],
        'i': [],
        'em': [],
        'u': [],
        's': [],
        'del': [],
        'code': []
    }

    # Remove all tags that are not in the allowed list
    for tag in soup.find_all(True):
        if tag.name not in allowed_tags:
            tag.unwrap()
    
    # Remove disallowed attributes from allowed tags
    for tag in soup.find_all(True):
        if tag.name in allowed_tags:
            allowed_attrs = allowed_tags[tag.name]
            tag.attrs = {attr: value for attr, value in tag.attrs.items() if attr in allowed_attrs}

    # Remove excessive <br> tags between block-level elements
    block_tags = ['p', 'blockquote', 'ul', 'ol']
    for br in soup.find_all('br'):
        # Handle <br> tags between two block-level elements
        prev_sibling = br.find_previous_sibling()
        next_sibling = br.find_next_sibling()

        # Skip over whitespace to find the actual previous tag
        while isinstance(prev_sibling, NavigableString) and prev_sibling.strip() == '':
            prev_sibling = prev_sibling.find_previous_sibling()

        # Skip over whitespace to find the actual next tag
        while isinstance(next_sibling, NavigableString) and next_sibling.strip() == '':
            next_sibling = next_sibling.find_next_sibling()

        if prev_sibling and prev_sibling.name in block_tags and \
           next_sibling and next_sibling.name in block_tags:
            br.decompose()

    # Remove consecutive <br> tags
    for br in soup.find_all('br'):
        next_s = br.next_sibling
        while next_s and isinstance(next_s, NavigableString) and not next_s.strip():
            next_s = next_s.next_sibling
        if next_s and next_s.name == 'br':
            next_s.decompose()

    return str(soup)